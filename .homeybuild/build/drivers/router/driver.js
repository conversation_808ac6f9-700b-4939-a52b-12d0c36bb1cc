"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const homey_1 = __importDefault(require("homey"));
class RouterDriver extends homey_1.default.Driver {
    /**
     * onInit is called when the driver is initialized.
     */
    async onInit() {
        this.log('Router driver has been initialized');
    }
    /**
     * onPairListDevices is called when a user is adding a device and the 'list_devices' view is called.
     * This should return an array with the data of devices that are available for pairing.
     */
    async onPairListDevices() {
        // For routers, we typically don't discover them automatically
        // Instead, we let the user enter the IP address and credentials during pairing
        return [];
    }
    /**
     * Handle the pairing process
     */
    async onPair(session) {
        let settings = {
            ip: '',
            username: '',
            password: '',
        };
        // Register listener for when the user submits router credentials
        session.setHandler('validate_router_credentials', async (data) => {
            try {
                const { ip, password } = data;
                const username = ''; // Not used for password-only authentication
                this.log(`Validating router credentials - IP: ${ip}, Password: ${password ? '******' : 'not set'}`);
                // Store settings for device creation
                settings = { ip, username, password };
                this.log('Stored settings for device creation');
                // Try to connect to the router to validate credentials
                this.log('Importing RouterApiClient');
                const RouterApiClient = (await Promise.resolve().then(() => __importStar(require('../../lib/RouterApiClient')))).RouterApiClient;
                this.log('Creating new RouterApiClient instance');
                const client = new RouterApiClient(ip, username, password);
                this.log('Initializing client to test connection');
                await client.init();
                // If we get here, the connection was successful
                this.log('Router connection validated successfully');
                return true;
            }
            catch (error) {
                this.error('Failed to validate router credentials:', error);
                return false;
            }
        });
        // Register listener for when the user adds the device
        session.setHandler('add_device', async () => {
            this.log('Adding device with settings:', settings);
            // Create device ID from IP address
            const deviceId = `router-${settings.ip.replace(/\./g, '-')}`;
            this.log(`Generated device ID: ${deviceId}`);
            // Return device data for createDevice
            const deviceData = {
                name: 'Ruijie X32-PRO Router',
                data: {
                    id: deviceId,
                },
                settings: settings,
            };
            this.log('Returning device data for creation:', deviceData);
            return deviceData;
        });
    }
}
module.exports = RouterDriver;
//# sourceMappingURL=driver.js.map