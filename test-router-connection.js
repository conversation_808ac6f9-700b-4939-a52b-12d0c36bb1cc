#!/usr/bin/env node

/**
 * Test script to debug the Ruijie X32-PRO router connection
 * This script helps identify issues with the RouterApiClient implementation
 */

const { RouterApiClient } = require('./build/lib/RouterApiClient');

// Configuration - Update these values for your router
const ROUTER_CONFIG = {
  ip: '*************',
  username: '', // Not used for password-only auth
  password: 'admin' // Replace with your actual router password
};

async function testRouterConnection() {
  console.log('=== Ruijie X32-PRO Router Connection Test ===\n');
  
  console.log(`Testing connection to router at: ${ROUTER_CONFIG.ip}`);
  console.log(`Using password: ${ROUTER_CONFIG.password ? '******' : 'not set'}\n`);

  const client = new RouterApiClient(
    ROUTER_CONFIG.ip,
    ROUTER_CONFIG.username,
    ROUTER_CONFIG.password
  );

  // Set up event listeners for debugging
  client.on('connected', () => {
    console.log('✅ Router connected successfully');
  });

  client.on('disconnected', () => {
    console.log('❌ Router disconnected');
  });

  client.on('error', (error) => {
    console.error('🚨 Router error:', error.message);
  });

  try {
    console.log('Step 1: Initializing connection...');
    await client.init();
    console.log('✅ Connection initialized successfully\n');

    console.log('Step 2: Getting router status...');
    const status = await client.getStatus();
    console.log('✅ Router status retrieved successfully');
    console.log('Router Status:');
    console.log(`  - Model: ${status.model}`);
    console.log(`  - Firmware: ${status.firmwareVersion}`);
    console.log(`  - Uptime: ${status.uptime} seconds`);
    console.log(`  - WAN IP: ${status.wanIp}`);
    console.log(`  - LAN IP: ${status.lanIp}`);
    console.log(`  - CPU Usage: ${status.cpuUsage}%`);
    console.log(`  - Memory Usage: ${status.memoryUsage}%`);
    console.log(`  - Guest WiFi: ${status.guestWifiEnabled ? 'Enabled' : 'Disabled'}`);
    console.log(`  - Connected Devices: ${status.connectedDevices.length}\n`);

    if (status.connectedDevices.length > 0) {
      console.log('Connected Devices:');
      status.connectedDevices.forEach((device, index) => {
        console.log(`  ${index + 1}. ${device.name} (${device.mac}) - ${device.ip} [${device.connectionType}]`);
      });
      console.log('');
    }

    console.log('Step 3: Testing guest WiFi control...');
    const currentGuestWifiStatus = status.guestWifiEnabled;
    console.log(`Current guest WiFi status: ${currentGuestWifiStatus ? 'Enabled' : 'Disabled'}`);
    
    // Toggle guest WiFi (be careful with this in production)
    console.log('Toggling guest WiFi...');
    await client.setGuestWifi(!currentGuestWifiStatus);
    console.log(`✅ Guest WiFi toggled to: ${!currentGuestWifiStatus ? 'Enabled' : 'Disabled'}`);
    
    // Wait a moment and toggle back
    console.log('Waiting 3 seconds before toggling back...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    await client.setGuestWifi(currentGuestWifiStatus);
    console.log(`✅ Guest WiFi restored to: ${currentGuestWifiStatus ? 'Enabled' : 'Disabled'}\n`);

    console.log('Step 4: Testing device connection check...');
    if (status.connectedDevices.length > 0) {
      const testDevice = status.connectedDevices[0];
      const isConnected = await client.isDeviceConnected(testDevice.mac);
      console.log(`✅ Device ${testDevice.name} (${testDevice.mac}) is ${isConnected ? 'connected' : 'not connected'}\n`);
    } else {
      console.log('No devices to test connection check with\n');
    }

    console.log('🎉 All tests completed successfully!');
    console.log('Your router driver should work properly with Homey.');

  } catch (error) {
    console.error('\n❌ Test failed with error:', error.message);
    console.error('Stack trace:', error.stack);
    
    console.log('\n🔧 Troubleshooting suggestions:');
    console.log('1. Verify the router IP address is correct and accessible');
    console.log('2. Check that the password is correct');
    console.log('3. Ensure the router web interface is accessible at http://*************/cgi-bin/luci/');
    console.log('4. Try accessing the router web interface manually in a browser');
    console.log('5. Check if the router firmware version affects the LuCI interface structure');
  } finally {
    // Clean up
    client.stopConnectionCheck();
    console.log('\nTest completed.');
  }
}

// Handle command line arguments
if (process.argv.length > 2) {
  const args = process.argv.slice(2);
  if (args.includes('--help') || args.includes('-h')) {
    console.log('Usage: node test-router-connection.js [--ip=IP] [--password=PASSWORD]');
    console.log('');
    console.log('Options:');
    console.log('  --ip=IP           Router IP address (default: *************)');
    console.log('  --password=PASS   Router password (default: admin)');
    console.log('  --help, -h        Show this help message');
    process.exit(0);
  }
  
  args.forEach(arg => {
    if (arg.startsWith('--ip=')) {
      ROUTER_CONFIG.ip = arg.split('=')[1];
    } else if (arg.startsWith('--password=')) {
      ROUTER_CONFIG.password = arg.split('=')[1];
    }
  });
}

// Run the test
testRouterConnection().catch(error => {
  console.error('Unhandled error:', error);
  process.exit(1);
});
